<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户协议</title>
</head>

<body>
    <h1>用户协议</h1>
    <p><i>西安成图科技有限公司</i>（以下简称"我们"）依据本协议为用户（以下简称"你"）提供<i>
            <script>document.write(new URLSearchParams(window.location.search).get('product_name') || '餐厅日记')</script>
        </i>服务。本协议对你和我们均具有法律约束力。</p>
    <h4>一、本服务的功能</h4>
    <p>你可以使用本服务
        <script>
            document.write(new URLSearchParams(window.location.search).get('service') || '菜谱学习')
        </script>。
    </p>
    <h4>二、责任范围及限制</h4>
    <p>你使用本服务得到的结果仅供参考，实际情况以官方为准。</p>
    <h4>三、隐私保护</h4>
    <p>我们重视对你隐私的保护，你的个人隐私信息将根据<a id="privacy-link-1" href="https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/privacy_product.html">《隐私政策》</a>受到保护与规范，详情请参阅<a id="privacy-link-2" href="https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/privacy_product.html">《隐私政策》</a>。</p>
    <script>
        // 获取当前URL的查询参数
        const currentQuery = window.location.search;
        // 为隐私政策链接添加当前URL的查询参数
        document.getElementById('privacy-link-1').href = "https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/privacy_product.html" + currentQuery;
        document.getElementById('privacy-link-2').href = "https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/privacy_product.html" + currentQuery;
    </script>
    <h4>四、其他条款</h4>
    <p>4.1 本协议所有条款的标题仅为阅读方便，本身并无实际涵义，不能作为本协议涵义解释的依据。</p>
    <p>4.2 本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。</p>
</body>

</html>