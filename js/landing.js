var link_id = getQueryVariable('linkId'),
    pkg = getQueryVariable('pkg'),
    report_key = getQueryVariable('report_key'),
    page = getQueryVariable('page'),
    rtype = getQueryVariable('rtype'),
    channel = getQueryVariable('channel'),
    userEntry = getQueryVariable('userEntry'),
    uctrackid = getQueryVariable('uctrackid'),
    configId = getQueryVariable('configId');
var paramsData = {},
    appInfo = {
        cname: "",
        short: "",
        cpm: ""
    };
var ua = navigator.userAgent.toLowerCase(),
    model = get_build(ua),
    factorystr = factory(ua); // userAgent，机型，厂商
var is_feedback = getCookie("is_feedback" + HexToStr(pkg) + link_id) == 1 ? 1 : 0; // 投诉标识
var isHapJump = false;
const API_ENDPOINT = "https://c.unity.cn";

const APP_ID = "1156179e-6f72-4b30-940a-8f7fca6efaa5";
const APP_SERVICE = "6c72cddc6a644625b7bc4e8980b48617";

const options = {
    method: 'get',
    url: `${API_ENDPOINT}/v1/configs/${configId}`,
    headers: {
        'Content-Type': 'application/json',
        ...get_basic_authorization(APP_ID, APP_SERVICE),
    },
    responseType: 'json',
};

var JsonData = {}; // json数据
try {
    var ajax = new XMLHttpRequest();
    ajax.open(options.method, options.url, false);

    for (const header in options.headers) {
        ajax.setRequestHeader(header, options.headers[header]);
    }
    ajax.send();
    JsonData = JSON.parse(JSON.parse(ajax.responseText).config.value)
} catch (e) {
    console.error(e);
    JsonData = {
        automatic: '1'
    }
}
console.log(JsonData.factor_switch);
launchFun()

webTrack() // 上报落地页打开数据
dataReport('start')
// 根据localStorage判定是否上报过 factorystr
if (!localStorage.getItem('report_factorystr')) {
    dataReport(factorystr)
    localStorage.setItem('report_factorystr', 'true')
}

// 如果uctrackid不存在或者为空则上报
if (!uctrackid) {
    dataReport('noUtrackid')
}

//dataKey，iframe跳转组件在该H5页面中的唯一标识
var dataKey = "my-router-btn"


let QkBtnImg = JsonData.button

//vivo jumpToken
let vivoJumpToken = []

let jumpToken = ''

function qkButton() {
    paramsData["uniqueId"] = genAESUniqueId() // hapkey (aes加密)
    paramsData["ts"] = new Date().getTime(); // 更新时间戳
    return `
			<qa-router-button id="app-btn" data-key=${dataKey} data-package-name=${pkg} data-page=/
				data-params={} data-design-params='{"fontSize": 16,"designWidth": 1080}'
				data-click-event='{"eventName": "handleClickEvent", "eventParams": "anyString"}'
				data-expose-event='{"eventName": "handleExposeEvent", "eventParams": "anyString"}'
				>
				<templates>
					<div class="btn-box">
						<img src=${QkBtnImg}
							id="btn" class="btn an_scale">
					</div>
				</templates>
				<styles>
					.btn-box{
					width:100%;
					}
					img {
					display:block;
					width:80%;
					margin:0 auto;
					}
					.an_scale {
					animation-name: Scale;
					animation-iteration-count: infinite;
					animation-duration: 1500ms;
					animation-fill-mode: none;
					animation-timing-function: linear;
					}

					@keyframes Scale {
					0% {
					transform: scale(1);
					}

					50% {
					transform: scale(1.1);
					}

					100% {
					transform: scale(1);
					}
					}
				</styles>
			</qa-router-button>
		`
}

function parseUrl(url) {
    const result = {
        packageName: null,
        path: null,
        params: {}
    };

    const regex = /hap:\/\/app\/([^/]+)\/([^?]+)\??(.*)/;
    const match = url.match(regex);

    if (match) {
        result.packageName = match[1];
        result.path = match[2];

        const queryString = match[3];
        if (queryString) {
            queryString.split("&").forEach(pair => {
                const [key, value] = pair.split("=");
                result.params[key] = value;
            });
        }
    }

    return result;
}

if (JsonData.pull_link) {
    // 替换拉起
    try {
        const {
            packageName,
            path,
            params
        } = parseUrl(JsonData.pull_link);
        pkg = packageName;
        page = path;
        let ext_field_13 = {
            'aid': getQueryVariable('aid') || '',
            'campaignid': getQueryVariable('campaignid') || '',
            'channelId': getQueryVariable('channelId') || '',
            'cid': getQueryVariable('cid') || '',
            'clickid': getQueryVariable('clickid') || '',
            'linkId': getQueryVariable('linkId') || '',
            'rtype': getQueryVariable('rtype') || '',
            'utmSource': getQueryVariable('site') || ''
        }
        let ha_other = JSON.stringify(ext_field_13);
        let fir_ha_qui = getQueryVariable('pkg');
        let las_ha_qui = getQueryVariable('pkg');
        paramsData["intent"] = params.intent;
        paramsData["channelId"] = params.channelId;
        paramsData["ha_other"] = ha_other;
        paramsData["fir_ha_qui"] = fir_ha_qui;
        paramsData["las_ha_qui"] = las_ha_qui;
    } catch (e) { }
} else {
    getQueryParams() // 解析query
}

try {
    jumpToken = vivoJumpToken.filter((item) => {
        return pkg == item.jumpPkg
    })[0]['jumpToken']
} catch (e) {

}

if (!jumpToken) {
    jumpToken = JsonData.pull_link ? JsonData.pull_jump_token : JsonData.source_jump_token
}

document.getElementById('btn-area').innerHTML = qkButton();

var companyEle = document.getElementById('company'),
    versionEle = document.getElementById('version'),
    bgEle = document.getElementById('bg'),
    btnEle = document.getElementById('btn'),
    complainEle = document.getElementById('complain'),
    appnameEle = document.getElementById('appname'),
    isShowView = document.getElementsByClassName('isShowView'),
    pageBackEle = document.getElementById('page-back'),
    des = document.getElementById('des');
if (rtype && rtype.indexOf('vivo') > -1) {
    pageBackEle.style.display = 'block'
}

if (JsonData && (JsonData.background || (JsonData.backgrounds && JsonData.backgrounds.length))) {
    if (JsonData.factor_switch && JsonData.factor_switch != 1) {
        for (var i = 0; i < isShowView.length; i++) {
            isShowView[i].innerHTML = ''
        }
    } else {
        if (companyEle) {
            companyEle.innerHTML = '开发者信息：' + JsonData.company
        }
        if (versionEle) {
            versionEle.innerHTML = 'app版本信息：' + JsonData.version
        }
        if (appnameEle) {
            appnameEle.innerHTML = '快应用app名称：' + JsonData.app
        }
        if (des) {
            des.innerHTML = JsonData.des
        }
    }
    if (bgEle) {
        bgEle.setAttribute('src', JsonData.background)
    }
    if (complainEle) {
        complainEle.setAttribute('src', JsonData.complain)
    }
    appInfo.cname = JsonData.app
    appInfo.short = JsonData.short
    appInfo.cpm = JsonData.company
} else {
    if (bgEle) {
        bgEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/default.jpg')
    }
    if (complainEle) {
        complainEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/feed.png')
    }
}

let pageHide = false;
let pageShowCount = 1;
let pageHideCount = 0;
var launchHapCount = 0;
let launchHapVivoCount = 0;
let launchClickCount = 0;
let launchIframeCount = 0;

// 拉起快应用
// if (is_feedback) {
//     window.location.replace("index4041.html"); // 投诉用户
// } else {
var date = GetDate(),
    is_open = getCookie("hp_time_" + HexToStr(pkg) + link_id + date);
// 自动拉起 automatic  1：自动拉起，0：手动拉起
if (JsonData.automatic == 1) {
    // 限制拉起 autocookie  1：只拉起一次，0：不限制
    if (JsonData.autocookie == 1) {
        if (!is_open) {
            openSpk()
            setCookie("hp_time_" + HexToStr(pkg) + link_id + date, 1, (JsonData.time ? JsonData.time : 86400) *
                1000)
        }
    } else {
        openSpk()
    }
}
// }

function clickHap() {
    dataReport('launchHap_pv');
    if (!isHapJump) {
        isHapJump = true;
        dataReport('launchHap_uv');
    }
    paramsData["uniqueId"] = genAESUniqueId() // hapkey (aes加密)
    paramsData["ts"] = new Date().getTime(); // 更新时间戳
    window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams(paramsData);
}

//自动拉起方法
function launchFun() {
    webTrack(`launchReady`);
    paramsData["uniqueId"] = genAESUniqueId() // hapkey (aes加密)
    paramsData["ts"] = new Date().getTime(); // 更新时间戳
    try {
        document.getElementById('bodyclickArea').addEventListener('click', clickHap);
    } catch (e) { }
    jumpPage('hap');
    try {
        isEnvSupportRouter(function (bAvailable) {
            //否支持iframe组件跳转快应用能力检测的回调函数，如支持则返回 true 值，否则返回 false 值
            if (bAvailable) {
                //routeToQuickapp接口即可拉起快应用[页面未隐藏]
                setTimeout(() => {
                    if (!pageHide) {
                        routeToQuickapp(dataKey);
                        launchHapVivoCount++;
                        webTrack(`launchH5Button_${launchHapVivoCount}`);
                    } else {
                        webTrack(`launchH5ButtonIgnore_${launchHapVivoCount}`);
                    }
                }, 300)
            }
        })
    } catch (e) {
        webTrack(`launchH5ButtonError`);
    }
}

function nt() {
    paramsData["uniqueid"] = genuniqueid() //加密id
    paramsData["uniqueId"] = genAESUniqueId() // hapkey (aes加密)
    paramsData["ts"] = new Date().getTime(); // 拉起时间戳
    if (getQueryVariable('adid')) {
        paramsData["utm_ad_id"] = getQueryVariable('adid')
    }
    var linkType = 'hap';
    launchFun();
}

//拉起三次

var ksBackThree = JsonData.back_three;

//无限拉起

var ksBackNoLimit = JsonData.back_no_limit;

var isInKs = ua.includes('kswebview') || ua.includes('nebula');


//监听页面隐藏和显示
webTrack(`pageShow_${pageShowCount}`);
try {
    if (typeof document.hidden !== "undefined") {
        visibilityChange_str = "hidden";
        visibilityChange = "visibilitychange";
    } else if (typeof document.mozHidden !== "undefined") {
        visibilityChange_str = "mozHidden";
        visibilityChange = "mozvisibilitychange";
    } else if (typeof document.msHidden !== "undefined") {
        visibilityChange_str = "msHidden";
        visibilityChange = "msvisibilitychange";
    } else if (typeof document.webkitHidden !== "undefined") {
        visibilityChange_str = "webkitHidden";
        visibilityChange = "webkitvisibilitychange";
    }
    document.addEventListener(
        visibilityChange,
        function () {
            if (!document[visibilityChange_str]) {
                paramsData["uniqueId"] = genAESUniqueId() // hapkey (aes加密)
                paramsData["ts"] = new Date().getTime(); // 更新时间戳
                bgEle.setAttribute('src', JsonData.backgrounds && JsonData.backgrounds.length ? randomItem(JsonData
                    .backgrounds) : JsonData.background)
                document.getElementById('btn-area').innerHTML = '';
                QkBtnImg = JsonData.buttons && JsonData.buttons.length ? randomItem(JsonData.buttons) : JsonData
                    .button;
                document.getElementById('btn-area').innerHTML = qkButton();
                console.log('页面显示');
                try {
                    if (ksBackThree) {
                        //拉三次
                        var launchTimesFlag = getCookie('launchTimesFlag') || 1;
                        var newlaunchTimesFlag = Number(launchTimesFlag) + 1;
                        if (Number(newlaunchTimesFlag) <= 3) {
                            launchFun();
                            var currentDate = new Date();
                            var midnight = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate
                                .getDate() + 1, 0, 0, 0);
                            var expires = midnight.toUTCString();
                            document.cookie = "launchTimesFlag=" + newlaunchTimesFlag + "; expires=" + expires +
                                "; path=/";
                        } else {
                            backSourceAppCommon();
                        }
                    } else {
                        if (ksBackNoLimit) {
                            //无限拉起
                            launchFun();
                        } else {
                            //拉起一次
                            backSourceAppCommon();
                        }
                    }
                } catch (e) {
                    webTrack('backLaunchError');
                }
                pageShowCount++;
                webTrack(`pageShow_${pageShowCount}`);
                if (pageHide) {
                    pageHide = false;
                }
            } else {
                console.log('页面隐藏');
                pageHideCount++;
                webTrack(`pageHide_${pageHideCount}`);
                pageHide = true;
            }
        },
    );
} catch (e) {
    console.log(e);
}

//返回来源包App
function backSourceAppCommon() {
    if (isInKs) {
        var isKsJs = ua.includes('nebula');
        if (isKsJs) {
            openScheme('ksnebula://home/<USER>'); //快手极速版
        } else {
            openScheme('kwai://home/<USER>'); //快手
        }
    }
}

//打开来源包Scheme
function openScheme(schemeUrl) {
    var link = document.createElement('a');
    link.href = schemeUrl;
    document.body.appendChild(link);
    link.click();
}

//创建iframe
function aif(e, id = '', is_del = 0) {
    var a = document["createElement"]("iframe");
    a["id"] = id;
    a["style"]["display"] = "none";
    a["src"] = e;
    var b = document["getElementsByTagName"]("body")[0];
    if (b) {
        b["appendChild"](a)
    } else {
        window["onload"] = function () {
            document["body"]["appendChild"](a)
        }
    }
}
// 唤醒落地页
function jumpPage(type) {
    try {
        switch (type) {
            case 'hap':
                launchHapCount++;
                webTrack(`launchHap_${launchHapCount}`);
                dataReport('launchHap_pv');
                if (!isHapJump) {
                    isHapJump = true;
                    dataReport('launchHap_uv');
                }
                window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams(paramsData);
                break;
            default:
                launchHapCount++;
                webTrack(`launchHap_${launchHapCount}`);
                window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams(paramsData)
                break;
        }
    } catch (e) {
        webTrack('error')
    }

}

function hiappLaunch() {
    let o = 'hwfastapp://' + pkg + '/' + page + '?' + httpBuildParams(paramsData);
    o = encodeURIComponent(o);
    window.location.href =
        "hiapp://com.huawei.appmarket?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" +
        o + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
}

function jumpPageHw(type) {
    jumpPage('hap');
    setTimeout(() => {
        try {
            switch (type) {
                case 'hap':
                    launchHapCount++;
                    webTrack(`launchHap_${launchHapCount}`);
                    window.location.href = 'hwfastapp://' + pkg + '/' + page + '?' + httpBuildParams(
                        paramsData);
                    break;
                default:
                    launchHapCount++;
                    webTrack(`launchHap_${launchHapCount}`);
                    window.location.href = 'hwfastapp://' + pkg + '/' + page + '?' + httpBuildParams(
                        paramsData);
                    break;
            }
        } catch (e) {
            webTrack('error')
        }
    }, 100)
}
// 投诉按钮点击事件
function clickComplaint() {
    webTrack('clickComplaint')
    window.location.replace('complaint.html');
}
// 启动拉起
function openSpk() {
    if (!is_feedback) {
        nt();
    } else {
        window.location.replace("index4041.html");
    }
    webTrack('open')
}
// 按钮点击
function handleClickEvent() {
    webTrack('click');
    launchClickCount++;
    webTrack(`launchClick_${launchClickCount}`);
    if (['tg_4161', 'tg_4133'].includes(getQueryVariable('linkId')) && getQueryVariable('pkg') ==
        'com.qudianread.home') {
        jumpPageHw('hap');
    }
}

// 点击组件曝光时回调执行的方法

function handleExposeEvent() {
    paramsData["uniqueId"] = genAESUniqueId() // hapkey (aes加密)
    paramsData["ts"] = new Date().getTime(); // 更新时间戳
    launchIframeCount++;
    webTrack(`handleExpose_${launchIframeCount}`);
}

//  工具模块

function randomItem(arr) {
    const randomIndex = Math.floor(Math.random() * arr.length);
    return arr[randomIndex]
}

function getQueryParams() {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] != 'page' && pair[0] != 'pkg' && pair[0] != 'automatic' && pair[0] != 'autocookie' && pair[0] !=
            'web_id' && pair[0] != 'time') {
            paramsData[`${pair[0]}`] = decodeURIComponent(pair[1])
        }
    }
}

function HexToStr(str) {
    var reg = /\%([0-9a-zA-Z]{2})/g
    var newStr = str
    while (res = reg.exec(str)) {
        newStr = newStr.replace(res[0], String.fromCharCode(parseInt(res[1], 16)))
    }
    return newStr
}

function GetDate(tm = 0) {
    var tieml = new Date();
    var now = new Date((tieml / 1000 + (tm * 86400)) * 1000) // 选择后加一天
    var year = now.getFullYear(); //年
    var month = now.getMonth() + 1; //月
    var day = now.getDate(); //日
    return year + "-" + month + "-" + day;
}

function setCookie(name, value, expire) {
    var exp = new Date();
    exp.setTime(exp.getTime() + expire);
    document.cookie = name + "=" + escape(value) + ";path=/;expires=" + exp.toGMTString();

    setlocalStoryage(name, value, 10)
}

function getCookie(name) {
    var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
    var lret = getlocalStoryage(name);
    if (arr != null) return unescape(arr[2]);
    if (!lret) return lret;
    return null;
}
/**
 * 设置 localStoryage
 * @param key 键
 * @param value 值
 * @param expire 过期时间(秒) 缓存最大时间请勿超过 forceTime 否则每次都会清空数据 (未设置请忽略)
 * @return bool true
 */
function setlocalStoryage(key, value = '', expire = 0) {
    // 当前时间戳
    var nowTime = Math.ceil(Date.now() / 1000);
    // 设置数据
    localStorage.setItem(key, JSON.stringify(value))
    // 判断是否有过期时间
    if (expire > 0) {
        // 设置过期时间
        localStorage.setItem(key + '_expire', (nowTime + parseInt(expire)))
    } else {
        // 设置过期时间
        localStorage.setItem(key + '_expire', 0)
    }
    return true;
}
/**
 * 读取 localStoryage
 * @param key
 * @return bool|originData false|originData
 */
function getlocalStoryage(key) {
    // 当前时间戳
    var nowTime = Math.ceil(Date.now() / 1000);
    // 获取键时间戳
    var rawCacheDataExpire = localStorage.getItem(key + '_expire');
    var cacheDataExpire = parseInt(rawCacheDataExpire);

    // 强制过期时间 为0时忽略 用于解决缓存时间与本地时间差距过大(例本地更改了计算机时间)
    var forceTime = 365 * 24 * 3600;
    // 判断用户是否删除了过期时间 判断是否设置了过期时间 判断是否超过过期时间 判断当前计算机时间与设置的过期时间差距是否过大
    if ((rawCacheDataExpire === null) || (cacheDataExpire > 0) && ((nowTime > cacheDataExpire) || (forceTime > 0 &&
        Math.abs(cacheDataExpire - nowTime) > forceTime))) {
        // 删除过期key
        localStorage.removeItem(key)
        // 删除过期时间
        localStorage.removeItem(key + '_expire')
        return false;
    }

    // 获取数据
    cacheData = JSON.parse(localStorage.getItem(key));

    if (cacheData === null || cacheData === false) {
        return false;
    }
    // 返回数据
    return cacheData;
}

function genuniqueid() {
    var uniqueid = getCookie('uniqueid' + HexToStr(pkg) + link_id);
    if (uniqueid) {
        return uniqueid;
    } else {
        var str = Number(Math.random().toString().substr(3, 32) + Date.now()).toString(36);
        setCookie('uniqueid' + HexToStr(pkg) + link_id, str, 365 * 24 * 60 * 60 * 1000);
        return str;
    }
}

function mathrandom(min, max) {
    return Math.floor(Math.random() * (max - min)) + min;
}

function genAESUniqueId() {
    try {
        var timestamp = new Date().getTime();
        var rand_str = mathrandom(10000000, 99999999);
        var str = link_id + ":" + link_id + ":" + timestamp + ":" + rand_str;

        // 算法是 AES-128-CBC
        var aes_key = CryptoJS.enc.Utf8.parse("00F35FCAA967B789"); //十六位十六进制数作为密钥
        var aes_iv = CryptoJS.enc.Utf8.parse('A041FCA49EF0B983');; //十六位十六进制数作为密钥偏移量

        var srcs = CryptoJS.enc.Utf8.parse(str);
        var encryptedData = CryptoJS.AES.encrypt(srcs, aes_key, {
            iv: aes_iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });
        return encryptedData.toString();
    } catch (err) {
        var timestamp = new Date().getTime();
        var rand_str = mathrandom(10000000, 99999999);
        var str = link_id + ":" + link_id + ":" + timestamp + ":" + rand_str;

        return window.btoa(str);
    }
}

function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return decodeURIComponent(pair[1]);
        }
    }
    return (false);
}

function get_build(ua) {
    var build = ua.split("build");
    var aaa = build[0].split(";")
    var bbb = aaa.pop()
    return bbb;
}
// oppo 判断
function isoppo(ua) {
    if (/(oppo|heytap)/g.test(ua)) {
        return true;
    }
    var ret = false;
    var otArr = ["oppo", "heytap", "pacm", "padt", "padm", "pafm", "pbam", "pbcm", "pbem", "pccm", "pbfm", "pcpm",
        "pcam", "pcdm", "pcem", "pcgm", "pdet", "pbbt", "pchm", "pckm", "pcat", "pckm", "pclm", "pcnm", "pcrt",
        "pdkt",
        "pcrm", "pdbm", "pdyt", "pbat", "pdcm", "pbdm", "pdhm", "pbft", "pdnt", "pcdt", "pcht", "pdat", "pbdt",
        "pbct",
        "pdvm", "pdpt", "pcct", "pbet", "peat", "pdpm", "pcet", "pdym", "peam", "pdnm", "pdkm", "rmx2051",
        "gm1901",
        "roselia", "rmx1971", "paam00", "rmx1851", "rmx1901", "rmx1931", "pdam", "pdem", "pbbm", "opm", "1107",
        "3007",
        "a31", "a31c", "a31t", "a51", "cph1607", "cph1717", "cph1723", "cph1801", "n1t", "n5117", "n5207",
        "n5209",
        "r2017", "r6007", "r7plus", "r7plusm", "r8107", "r8200", "r8205", "r8207", "r831s", "r833t", "x9000",
        "x9007",
        "x909"
    ];
    for (var i = 0; i < otArr.length; i++) {
        if (ua.indexOf(otArr[i]) > -1) {
            ret = true;
            break;
        }
    }
    return ret;
}
//是否是小米手机
function xiaomi(ua) {
    return 1 < (ua.match(/.*(xiaomi|redmi|mix|mi\s).*/i) || []).length
}
//是否是vivo手机
function isvivo(ua) {
    return /(vivo|; v1|; v2)/g.test(ua);
}

function factory(ua) {
    if (ua.indexOf("huawei") > -1) {
        return 'factory_huawei';
    }
    if (ua.indexOf('honor') > -1) {
        return 'factory_honor';
    }
    if (xiaomi(ua)) {
        return 'factory_xiaomi';
    }
    if (isvivo(ua)) {
        return 'factory_vivo';
    }
    if (isoppo(ua)) {
        return 'factory_oppo';
    }
    return 'factory_other';
}

function getQueryString(e) {
    if ((e = e || location.href).split("?").length <= 1) return {};
    var t = e.split("?")[1];
    if (!t) return {};
    for (var n = {},
        i = 0,
        a = (t = t.split("&")).length; i < a; i++) {
        var o = t[i].split("=");
        n["" + o[0]] = o[1]
    }
    return n
}
// 参数拼接处理
function buildParams(prefix, obj, traditional, add) {
    var name;
    //传入的是一个对象，这里是false
    if (Array.isArray(obj)) {
        obj.map(function (i, v) {
            if (traditional || /\[\]$/.test(prefix)) {
                add(prefix, v);
            } else {
                //传入参数形式为：buildParams1("students['']","a1",traditional,add)
                //也就是这时候在add方法里面的key就是students[]这种形式的!也就是说这时候的key就是stutents[]!
                //然后对这个key进行编码!
                buildParams(prefix + "[" + (typeof v === "object" ? i : "") + "]", v, traditional, add);
            }
        })
        //如果没有传入traditional同时还是对象
    } else if (!traditional && typeof obj === "object") {
        //那么就会再次进行深度遍历，然后遍历出所有的属性，所以这里是深度遍历的!
        for (name in obj) {
            buildParams(prefix + "[" + name + "]", obj[name], traditional, add);
        }
    } else {
        add(prefix, obj);
    }
}
// 参数处理，对象拼接成表单提交形式
function httpBuildParams(a, traditional) {
    return 'userEntry=' + userEntry + '&channel=' + channel + '&uctrackid=' + uctrackid + "&configId=" + configId + "&appid=" + report_key;
}

function getAndroidVersion(ua) {
    ua = (ua || navigator.userAgent).toLowerCase();
    var match = ua.match(/android\s([0-9\.]*)/);
    return match ? match[1] : false;
}
// 工具模块 ==========end=========

// 上报模块功能=====start===========
function webTrack(action) {
    if (!action) {
        action = 'view'
    }
    var querys = getQueryString()
    var defualtData = webTrackData()
    webTrackRequest('webland', Object.assign(querys, defualtData, {
        'scenario': action,
    }))
}

function webTrackData() {
    return {
        'pkg': HexToStr(pkg),
        'page': page,
        'lid': link_id,
        'brand': factorystr,
        'event': 'landweb',
        'product': model,
        'uniqueid': genuniqueid(),
        'osVersionCode': getAndroidVersion(),
        'timesTamp': new Date().getTime()
    }
}

function webTrackRequest(store, data) {
    // var img = document.createElement('img'),
    //     sls_web_tracking = JsonData.sls_web_tracking ? JsonData.sls_web_tracking :
    //     'https://reportcluster.cn-shenzhen.log.aliyuncs.com/logstores/reportcluster/track.gif?APIVersion=0.6.0';
    // img.src = sls_web_tracking + '&' + httpBuildParams(data);
    // img.width = 0;
    // img.height = 0;
    // document.body.appendChild(img)
}
// 上报模块功能============end=============

// app 用途说明
function licensing() {
    webTrack('licensing')
    window.location.href = `privacy/user_interface.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=${JsonData.app}`;
}
// app 隐私协议
function privacy() {
    webTrack('privacy')
    window.location.href = `privacy/privacy_product.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=${JsonData.app}`;
}

function pageBack() {
    history.go(-1)
}

function get_basic_authorization(app_id, app_secret) {
    /** 获取basic auth的Header */
    const credentials = `${app_id}:${app_secret}`;
    const encodedCredentials = btoa(credentials);
    return { 'Authorization': `Basic ${encodedCredentials}` };
}

function dataReport(event) {
    console.log('上报数据', event);
    if (!event) return;

    // 构建请求URL
    const reportUrl = `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${report_key}&report_name=${event}`;

    // 创建并发送异步请求
    fetch(reportUrl)
        .then(response => {
            if (response.ok) {
                console.log(`数据上报成功: ${event}`);
            } else {
                console.error(`数据上报失败: ${event}, 状态码: ${response.status}`);
            }
        })
        .catch(error => {
            console.error(`数据上报出错: ${event}`, error);
        });
}