// 配置相关的异步加载逻辑
(function() {
    'use strict';

    var loadCount = 1;
    var pageHide = false;
    // API配置
    const API_ENDPOINT = "https://c.unity.cn";
    const APP_ID = "1156179e-6f72-4b30-940a-8f7fca6efaa5";
    const APP_SERVICE = "6c72cddc6a644625b7bc4e8980b48617";

    // 获取基本认证头
    function get_basic_authorization(app_id, app_secret) {
        const credentials = `${app_id}:${app_secret}`;
        const encodedCredentials = btoa(credentials);
        return { 'Authorization': `Basic ${encodedCredentials}` };
    }

    // 数据上报函数
    function dataReport(event) {
        console.log('上报数据', event);
        if (!event) return;

        const reportUrl = `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${report_key}&report_name=${event}`;

        fetch(reportUrl)
            .then(response => {
                if (response.ok) {
                    console.log(`数据上报成功: ${event}`);
                } else {
                    console.error(`数据上报失败: ${event}, 状态码: ${response.status}`);
                }
            })
            .catch(error => {
                console.error(`数据上报出错: ${event}`, error);
            });
    }

    // 设备判断函数
    function isoppo(ua) {
        if (/(oppo|heytap)/g.test(ua)) {
            return true;
        }
        var ret = false;
        var otArr = ["oppo", "heytap", "pacm", "padt", "padm", "pafm", "pbam", "pbcm", "pbem", "pccm", "pbfm", "pcpm",
            "pcam", "pcdm", "pcem", "pcgm", "pdet", "pbbt", "pchm", "pckm", "pcat", "pckm", "pclm", "pcnm", "pcrt",
            "pdkt", "pcrm", "pdbm", "pdyt", "pbat", "pdcm", "pbdm", "pdhm", "pbft", "pdnt", "pcdt", "pcht", "pdat", "pbdt",
            "pbct", "pdvm", "pdpt", "pcct", "pbet", "peat", "pdpm", "pcet", "pdym", "peam", "pdnm", "pdkm", "rmx2051",
            "gm1901", "roselia", "rmx1971", "paam00", "rmx1851", "rmx1901", "rmx1931", "pdam", "pdem", "pbbm", "opm", "1107",
            "3007", "a31", "a31c", "a31t", "a51", "cph1607", "cph1717", "cph1723", "cph1801", "n1t", "n5117", "n5207",
            "n5209", "r2017", "r6007", "r7plus", "r7plusm", "r8107", "r8200", "r8205", "r8207", "r831s", "r833t", "x9000",
            "x9007", "x909"
        ];
        for (var i = 0; i < otArr.length; i++) {
            if (ua.indexOf(otArr[i]) > -1) {
                ret = true;
                break;
            }
        }
        return ret;
    }

    function xiaomi(ua) {
        return 1 < (ua.match(/.*(xiaomi|redmi|mix|mi\s).*/i) || []).length
    }

    function isvivo(ua) {
        return /(vivo|; v1|; v2)/g.test(ua);
    }

    function factory(ua) {
        if (ua.indexOf("huawei") > -1) {
            return 'huawei';
        }
        if (ua.indexOf('honor') > -1) {
            return 'honor';
        }
        if (xiaomi(ua)) {
            return 'xiaomi';
        }
        if (isvivo(ua)) {
            return 'vivo';
        }
        if (isoppo(ua)) {
            return 'oppo';
        }
        return 'other';
    }


    // 异步加载配置数据
    function loadConfig() {
        const options = {
            method: 'get',
            url: `${API_ENDPOINT}/v1/configs/${configId}`,
            headers: {
                'Content-Type': 'application/json',
                ...get_basic_authorization(APP_ID, APP_SERVICE),
            },
            responseType: 'json',
        };

        var JsonData = {};
        try {
            var ajax = new XMLHttpRequest();
            ajax.open(options.method, options.url, true); // 改为异步

            for (const header in options.headers) {
                ajax.setRequestHeader(header, options.headers[header]);
            }

            ajax.onreadystatechange = function() {
                if (ajax.readyState === 4) {
                    if (ajax.status === 200) {
                        try {
                            JsonData = JSON.parse(JSON.parse(ajax.responseText).config.value);
                        } catch (e) {
                            console.error(e);
                            JsonData = { automatic: '1' };
                        }
                    } else {
                        JsonData = { automatic: '1' };
                    }

                    // 配置加载完成后更新UI
                    updateUI(JsonData);
                    setupPageVisibilityHandler(JsonData);
                }
            };

            ajax.send();
        } catch (e) {
            console.error(e);
            JsonData = { automatic: '1' };
            updateUI(JsonData);
            setupPageVisibilityHandler(JsonData);
        }
    }

    // 更新UI元素
    function updateUI(JsonData) {
        var companyEle = document.getElementById('company'),
            versionEle = document.getElementById('version'),
            bgEle = document.getElementById('bg'),
            complainEle = document.getElementById('complain'),
            appnameEle = document.getElementById('appname'),
            isShowView = document.getElementsByClassName('isShowView');

        if (JsonData && (JsonData.background || (JsonData.backgrounds && JsonData.backgrounds.length))) {
            if (JsonData.factor_switch && JsonData.factor_switch != 1) {
                for (var i = 0; i < isShowView.length; i++) {
                    isShowView[i].innerHTML = ''
                }
            } else {
                if (companyEle) {
                    companyEle.innerHTML = '开发者信息：' + JsonData.company
                }
                if (versionEle) {
                    versionEle.innerHTML = 'app版本信息：' + JsonData.version
                }
                if (appnameEle) {
                    appnameEle.innerHTML = '快应用app名称：' + JsonData.app
                }
            }
            if (bgEle) {
                bgEle.setAttribute('src', JsonData.background)
            }
            if (complainEle) {
                complainEle.setAttribute('src', JsonData.complain)
            }
        } else {
            if (bgEle) {
                bgEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/default.jpg')
            }
            if (complainEle) {
                complainEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/feed.png')
            }
        }
    }

    // 设置页面可见性处理
    function setupPageVisibilityHandler(JsonData) {
        var ksBackThree = JsonData.back_three;
        var ksBackNoLimit = JsonData.back_no_limit;

        try {
            var visibilityChange_str, visibilityChange;
            if (typeof document.hidden !== "undefined") {
                visibilityChange_str = "hidden";
                visibilityChange = "visibilitychange";
            } else if (typeof document.mozHidden !== "undefined") {
                visibilityChange_str = "mozHidden";
                visibilityChange = "mozvisibilitychange";
            } else if (typeof document.msHidden !== "undefined") {
                visibilityChange_str = "msHidden";
                visibilityChange = "msvisibilitychange";
            } else if (typeof document.webkitHidden !== "undefined") {
                visibilityChange_str = "webkitHidden";
                visibilityChange = "webkitvisibilitychange";
            }

            document.addEventListener(
                visibilityChange,
                function() {
                    if (!document[visibilityChange_str]) {
                        document.getElementById('btn-area').innerHTML = '';
                        document.getElementById('btn-area').innerHTML = qkButton();
                        console.log('页面显示');
                        try {
                            if (ksBackThree) {
                                if (loadCount <= 3) {
                                    loadCount++;
                                    launchFun();
                                }
                            } else {
                                if (ksBackNoLimit) {
                                    launchFun();
                                }
                            }
                        } catch (e) {}
                        if (pageHide) {
                            pageHide = false;
                        }
                    } else {
                        console.log('页面隐藏');
                        pageHide = true;
                    }
                },
            );
        } catch (e) {
            console.log(e);
        }
    }

    // localsotrage 检查有没有上报
    if (!localStorage.getItem('report_key')) {
        dataReport('h5_start');
        var ua = navigator.userAgent.toLowerCase();
        // 获取设备信息
        var factorystr = factory(ua);
        dataReport('factory_' + factorystr);
        localStorage.setItem('report_key', '1');
    }

    // 如果uctrackid不存在或者为空则上报
    if (!uctrackid) {
        dataReport('noUtrackid');
    }

    // 异步加载配置
    loadConfig();
})();