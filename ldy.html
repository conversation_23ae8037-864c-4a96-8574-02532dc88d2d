<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Expires" content="-1" />
    <meta http-equiv="pragram" content="no-cache" />
    <meta name="referrer" content="unsafe-url">
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <title></title>
    <link rel="stylesheet" type="text/css" href="./css/style.css">
    <script>
        // 全局变量定义
        var pkg = getQueryVariable('pkg'),
            report_key = getQueryVariable('report_key'),
            page = getQueryVariable('page'),
            channel = getQueryVariable('channel'),
            userEntry = getQueryVariable('userEntry'),
            uctrackid = getQueryVariable('uctrackid'),
            configId = getQueryVariable('configId');
        var dataKey = "my-router-btn";
        let QkBtnImg = "./img/button.png";
        var isClickEventAdded = false; // 标记点击事件是否已添加

        // 工具函数 - 获取URL参数
        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return decodeURIComponent(pair[1]);
                }
            }
            return (false);
        }

        // 核心函数 - 创建qkButton
        function qkButton() {
            return `
                <qa-router-button id="app-btn" data-key=${dataKey} data-package-name=${pkg} data-page=/
                    data-params={} data-design-params='{"fontSize": 16,"designWidth": 1080}'
                    data-click-event='{"eventName": "handleClickEvent", "eventParams": "anyString"}'
                    data-expose-event='{"eventName": "handleExposeEvent", "eventParams": "anyString"}'
                    >
                    <templates>
                        <div class="btn-box">
                            <img src=${QkBtnImg}
                                id="btn" class="btn an_scale">
                        </div>
                    </templates>
                    <styles>
                        .btn-box{
                        width:100%;
                        }
                        img {
                        display:block;
                        width:80%;
                        margin:0 auto;
                        }
                        .an_scale {
                        animation-name: Scale;
                        animation-iteration-count: infinite;
                        animation-duration: 1500ms;
                        animation-fill-mode: none;
                        animation-timing-function: linear;
                        }

                        @keyframes Scale {
                        0% {
                        transform: scale(1);
                        }

                        50% {
                        transform: scale(1.1);
                        }

                        100% {
                        transform: scale(1);
                        }
                        }
                    </styles>
                </qa-router-button>
            `;
        }

        // 核心函数 - 跳转页面
        function jumpToApp() {
            try {
                window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
            } catch (e) {}
        }

        // 核心函数 - 执行路由器检查
        function executeRouterCheck() {
            try {
                if (typeof isEnvSupportRouter === 'function') {
                    isEnvSupportRouter(function(bAvailable) {
                        if (bAvailable) {
                            routeToQuickapp(dataKey);
                        }
                    });
                }
            } catch (e) {}
        }

        // 核心函数 - 点击快应用（合并逻辑）
        function clickHap() {
            console.log('clickHap - 用户点击触发');
            jumpToApp();
            executeRouterCheck();
        }

        // 核心函数 - 自动拉起方法
        function launchFun() {
            console.log('launchFun - 自动拉起');
            try {
                // 检查是否已经添加了点击事件
                if (!isClickEventAdded) {
                    document.getElementById('bodyclickArea').addEventListener('click', clickHap);
                    isClickEventAdded = true;
                }
            } catch (e) {}
            jumpToApp();
            executeRouterCheck();
        }

        // 核心函数 - 构建HTTP参数
        function httpBuildParams() {
            return 'userEntry=' + userEntry + '&channel=' + channel + '&uctrackid=' + uctrackid + "&appid=" + report_key + "&configId=" + configId;
        }

        // 按钮点击事件处理
        function handleClickEvent() {}

        function handleExposeEvent() {}

        // 快速初始化函数 - 在外部脚本加载完成后立即执行
        function quickInit() {
            // 立即创建qkButton
            var btnArea = document.getElementById('btn-area');
            if (btnArea) {
                btnArea.innerHTML = qkButton();
            }

            // 立即调用launchFun
            launchFun();

            // 异步加载配置和其他非关键资源
            loadConfigAndUI();
        }

        // 异步加载配置和UI内容
        function loadConfigAndUI() {
            // 动态加载其他JS文件
            var script = document.createElement('script');
            script.src = './js/config.js';
            script.onload = function() {
                console.log('配置文件加载完成');
            };
            document.head.appendChild(script);
        }

        // 外部脚本加载
        var script = document.createElement('script');
        script.src = 'https://jits5.heytapdownload.com/cms-jits-heytapmobi-com/iframe/qa_router.min.js';
        script.onload = function() {
            // 外部脚本加载完成后立即执行关键初始化
            quickInit();
        };
        script.onerror = function() {
            console.error('外部脚本加载失败');
            // 即使外部脚本加载失败，也尝试执行基本的初始化
            setTimeout(quickInit, 100);
        };
        document.head.appendChild(script);
    </script>
</head>

<body>
    <div class="page">
        <div style="position: relative;">
            <div id="page-back" onclick="pageBack()">返回</div>
            <div class="appinfo isShowView">
                <span id="appname">app名称</span>
                <span id="version">app版本信息</span>
            </div>
            <div class="appinfo isShowView">
                <span id="company">开发者信息</span>
                <span id='linkTypeFlag'></span>
            </div>
            <div class="appExp isShowView" style="text-align: center;">
                <span><a onclick="licensing()">app的权限列表及用途说明</a></span>
                <span><a onclick="privacy()">app的用户隐私协议</a></span>
            </div>
            <img src="" id="complain" class="ts" onclick="clickComplaint()">
            <div class="box-img" style="position: relative;" id="bodyclickArea">
                <img src="" id="bg" class="bg">
                <!--H5点击组件区域-->
                <div id="btn-area" style="position: absolute;top:125vw;width: 100vw;"></div>
            </div>
        </div>
    </div>

    <script>
        // 页面基本功能函数
        function pageBack() {
            history.go(-1);
        }

        function clickComplaint() {
            window.location.replace('complaint.html');
        }

        function licensing() {
            window.location.href = `privacy/user_interface.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=快应用`;
        }

        function privacy() {
            window.location.href = `privacy/privacy_product.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=快应用`;
        }
    </script>
</body>

</html>